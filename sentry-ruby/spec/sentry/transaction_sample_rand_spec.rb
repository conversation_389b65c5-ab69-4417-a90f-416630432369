# frozen_string_literal: true

RSpec.describe Sentry::Transaction do
  before do
    perform_basic_setup do |config|
      config.traces_sample_rate = 1.0
    end
  end

  let(:hub) { Sentry.get_current_hub }

  describe "sample_rand integration" do
    describe "#initialize" do
      it "generates sample_rand when not provided" do
        transaction = described_class.new(hub: hub)

        expect(transaction.instance_variable_get(:@sample_rand)).to be_a(Float)
        expect(transaction.instance_variable_get(:@sample_rand)).to be >= 0.0
        expect(transaction.instance_variable_get(:@sample_rand)).to be < 1.0
      end

      it "uses provided sample_rand" do
        sample_rand = 0.123456
        transaction = described_class.new(hub: hub, sample_rand: sample_rand)

        expect(transaction.instance_variable_get(:@sample_rand)).to eq(sample_rand)
      end

      it "generates deterministic sample_rand from trace_id" do
        trace_id = "abcdef1234567890abcdef1234567890"

        transaction1 = described_class.new(hub: hub, trace_id: trace_id)
        transaction2 = described_class.new(hub: hub, trace_id: trace_id)

        expect(transaction1.instance_variable_get(:@sample_rand)).to eq(
          transaction2.instance_variable_get(:@sample_rand)
        )
      end
    end

    describe ".from_sentry_trace" do
      let(:sentry_trace) { "abcdef1234567890abcdef1234567890-1234567890123456-1" }

      context "with sample_rand in baggage" do
        let(:baggage_header) { "sentry-trace_id=abcdef1234567890abcdef1234567890,sentry-sample_rand=0.123456" }

        it "uses sample_rand from baggage" do
          transaction = described_class.from_sentry_trace(sentry_trace, baggage: baggage_header, hub: hub)

          expect(transaction.instance_variable_get(:@sample_rand)).to eq(0.123456)
        end
      end

      context "without sample_rand in baggage" do
        let(:baggage_header) { "sentry-trace_id=abcdef1234567890abcdef1234567890,sentry-sample_rate=0.5" }

        it "generates deterministic sample_rand from trace_id" do
          transaction1 = described_class.from_sentry_trace(sentry_trace, baggage: baggage_header, hub: hub)
          transaction2 = described_class.from_sentry_trace(sentry_trace, baggage: baggage_header, hub: hub)

          expect(transaction1.instance_variable_get(:@sample_rand)).to eq(
            transaction2.instance_variable_get(:@sample_rand)
          )
        end

        it "generates sample_rand based on sampling decision when available" do
          # This tests the special case where sample_rand is missing but we have
          # sampling decision and rate
          transaction = described_class.from_sentry_trace(sentry_trace, baggage: baggage_header, hub: hub)
          sample_rand = transaction.instance_variable_get(:@sample_rand)

          # For sampled=true and sample_rate=0.5, sample_rand should be < 0.5
          expect(sample_rand).to be < 0.5
        end
      end
    end

    describe "#set_initial_sample_decision" do
      let(:transaction) { described_class.new(hub: hub, sample_rand: 0.3) }

      before do
        Sentry.configuration.traces_sample_rate = 0.5
      end

      it "uses sample_rand for sampling decision" do
        transaction.set_initial_sample_decision(sampling_context: {})

        # sample_rand (0.3) < sample_rate (0.5), so should be sampled
        expect(transaction.sampled).to be true
      end

      it "doesn't sample when sample_rand >= sample_rate" do
        transaction.instance_variable_set(:@sample_rand, 0.7)
        transaction.set_initial_sample_decision(sampling_context: {})

        # sample_rand (0.7) >= sample_rate (0.5), so should not be sampled
        expect(transaction.sampled).to be false
      end

      it "is deterministic for the same sample_rand and sample_rate" do
        transaction1 = described_class.new(hub: hub, sample_rand: 0.3)
        transaction2 = described_class.new(hub: hub, sample_rand: 0.3)

        transaction1.set_initial_sample_decision(sampling_context: {})
        transaction2.set_initial_sample_decision(sampling_context: {})

        expect(transaction1.sampled).to eq(transaction2.sampled)
      end

      context "with traces_sampler" do
        before do
          Sentry.configuration.traces_sampler = ->(context) { context[:parent_sample_rate] || 0.5 }
        end

        it "provides parent_sample_rate in sampling context" do
          baggage = Sentry::Baggage.new({ "sample_rate" => "0.25" })
          transaction = described_class.new(hub: hub, baggage: baggage, sample_rand: 0.3)

          context = nil
          allow(Sentry.configuration.traces_sampler).to receive(:call) do |ctx|
            context = ctx
            0.5
          end

          # Use the proper sampling context that includes parent_sample_rate
          sampling_context = {
            transaction_context: transaction.to_hash,
            parent_sampled: transaction.parent_sampled,
            parent_sample_rate: transaction.parent_sample_rate
          }
          transaction.set_initial_sample_decision(sampling_context: sampling_context)

          expect(context[:parent_sample_rate]).to eq(0.25)
        end
      end
    end

    describe "#parent_sample_rate" do
      it "returns sample_rate from baggage" do
        baggage = Sentry::Baggage.new({ "sample_rate" => "0.75" })
        transaction = described_class.new(hub: hub, baggage: baggage)

        expect(transaction.parent_sample_rate).to eq(0.75)
      end

      it "returns nil when no baggage" do
        transaction = described_class.new(hub: hub)

        expect(transaction.parent_sample_rate).to be_nil
      end

      it "returns nil when no sample_rate in baggage" do
        baggage = Sentry::Baggage.new({ "trace_id" => "abc123" })
        transaction = described_class.new(hub: hub, baggage: baggage)

        expect(transaction.parent_sample_rate).to be_nil
      end
    end

    describe "baggage population" do
      it "includes sample_rand in baggage" do
        transaction = described_class.new(hub: hub, sample_rand: 0.123456)
        baggage = transaction.get_baggage

        expect(baggage.items["sample_rand"]).to eq("0.123456")
      end
    end
  end
end
