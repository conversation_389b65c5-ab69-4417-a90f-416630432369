# frozen_string_literal: true

RSpec.describe "traces_sampler with sample_rand" do
  before do
    perform_basic_setup do |config|
      config.traces_sample_rate = 0.5
    end
  end

  let(:hub) { Sentry.get_current_hub }

  describe "traces_sampler callback" do
    context "with parent sampling context" do
      before do
        Sentry.configuration.traces_sampler = lambda do |sampling_context|
          # Inherit parent sample rate if available
          if sampling_context[:parent_sample_rate]
            sampling_context[:parent_sample_rate]
          else
            0.5
          end
        end
      end

      it "provides parent_sample_rate in sampling context" do
        # Create a transaction with baggage containing sample_rate
        baggage = Sentry::Baggage.new({"sample_rate" => "0.25"})
        transaction = Sentry::Transaction.new(hub: hub, baggage: baggage, sample_rand: 0.1)

        context = nil
        allow(Sentry.configuration.traces_sampler).to receive(:call) do |ctx|
          context = ctx
          0.25
        end

        # Start transaction through hub to get proper sampling context
        hub.start_transaction(transaction: transaction)

        expect(context[:parent_sample_rate]).to eq(0.25)
        expect(context[:parent_sampled]).to be_nil
        expect(context[:transaction_context]).to include(:trace_id, :span_id)
      end

      it "uses deterministic sampling with sample_rand" do
        # Create transaction with specific sample_rand
        transaction1 = Sentry::Transaction.new(hub: hub, sample_rand: 0.2)
        transaction2 = Sentry::Transaction.new(hub: hub, sample_rand: 0.2)

        # Both should have same sampling decision since they use same sample_rand
        hub.start_transaction(transaction: transaction1)
        hub.start_transaction(transaction: transaction2)

        expect(transaction1.sampled).to eq(transaction2.sampled)
      end

      it "respects traces_sampler return value with sample_rand" do
        # Set up traces_sampler to return 0.3
        Sentry.configuration.traces_sampler = ->(_) { 0.3 }

        # Test with sample_rand < 0.3 (should be sampled)
        transaction1 = Sentry::Transaction.new(hub: hub, sample_rand: 0.2)
        hub.start_transaction(transaction: transaction1)
        expect(transaction1.sampled).to be true

        # Test with sample_rand >= 0.3 (should not be sampled)
        transaction2 = Sentry::Transaction.new(hub: hub, sample_rand: 0.4)
        hub.start_transaction(transaction: transaction2)
        expect(transaction2.sampled).to be false
      end
    end

    context "inheriting parent sampling decision" do
      before do
        Sentry.configuration.traces_sampler = lambda do |sampling_context|
          # Example of proper parent sampling inheritance
          parent_sample_rate = sampling_context[:parent_sample_rate]
          if parent_sample_rate
            parent_sample_rate
          else
            0.5
          end
        end
      end

      it "maintains consistent sampling across trace" do
        # Simulate incoming trace with sample_rate
        sentry_trace = "771a43a4192642f0b136d5159a501700-7c51afd529da4a2a-1"
        baggage_header = "sentry-trace_id=771a43a4192642f0b136d5159a501700,sentry-sample_rate=0.25,sentry-sample_rand=0.1"

        # Create child transaction from incoming trace
        child_transaction = Sentry::Transaction.from_sentry_trace(sentry_trace, baggage: baggage_header, hub: hub)
        hub.start_transaction(transaction: child_transaction)

        # Should use parent sample rate (0.25) and sample_rand (0.1)
        # Since 0.1 < 0.25, should be sampled
        expect(child_transaction.sampled).to be true
        expect(child_transaction.effective_sample_rate).to eq(0.25)
      end

      it "generates consistent sample_rand when missing from parent" do
        # Simulate incoming trace without sample_rand but with sampling decision
        sentry_trace = "771a43a4192642f0b136d5159a501700-7c51afd529da4a2a-1"
        baggage_header = "sentry-trace_id=771a43a4192642f0b136d5159a501700,sentry-sample_rate=0.5"

        # Create multiple child transactions - should have same sample_rand
        child1 = Sentry::Transaction.from_sentry_trace(sentry_trace, baggage: baggage_header, hub: hub)
        child2 = Sentry::Transaction.from_sentry_trace(sentry_trace, baggage: baggage_header, hub: hub)

        expect(child1.instance_variable_get(:@sample_rand)).to eq(
          child2.instance_variable_get(:@sample_rand)
        )
      end
    end

    context "with boolean return values" do
      it "handles true return value" do
        Sentry.configuration.traces_sampler = ->(_) { true }

        transaction = Sentry::Transaction.new(hub: hub, sample_rand: 0.9)
        hub.start_transaction(transaction: transaction)

        # Should be sampled regardless of sample_rand when traces_sampler returns true
        expect(transaction.sampled).to be true
        expect(transaction.effective_sample_rate).to eq(1.0)
      end

      it "handles false return value" do
        Sentry.configuration.traces_sampler = ->(_) { false }

        transaction = Sentry::Transaction.new(hub: hub, sample_rand: 0.1)
        hub.start_transaction(transaction: transaction)

        # Should not be sampled regardless of sample_rand when traces_sampler returns false
        expect(transaction.sampled).to be false
        expect(transaction.effective_sample_rate).to eq(0.0)
      end
    end

    context "with invalid return values" do
      it "handles invalid return value gracefully" do
        Sentry.configuration.traces_sampler = ->(_) { "invalid" }

        transaction = Sentry::Transaction.new(hub: hub, sample_rand: 0.1)
        hub.start_transaction(transaction: transaction)

        # Should not be sampled when traces_sampler returns invalid value
        expect(transaction.sampled).to be false
      end
    end
  end

  describe "sampling context data" do
    before do
      Sentry.configuration.traces_sampler = lambda do |sampling_context|
        @last_sampling_context = sampling_context
        0.5
      end
    end

    it "includes all required context data" do
      transaction = Sentry::Transaction.new(
        hub: hub,
        name: "test_transaction",
        op: "test_op",
        sample_rand: 0.3
      )

      hub.start_transaction(transaction: transaction)

      expect(@last_sampling_context).to include(
        :transaction_context,
        :parent_sampled,
        :parent_sample_rate
      )

      expect(@last_sampling_context[:transaction_context]).to include(
        :name,
        :op,
        :trace_id,
        :span_id
      )
    end

    it "includes custom sampling context" do
      custom_context = { user_id: "123", feature_flag: true }

      transaction = Sentry::Transaction.new(hub: hub, sample_rand: 0.3)
      hub.start_transaction(transaction: transaction, custom_sampling_context: custom_context)

      expect(@last_sampling_context[:user_id]).to eq("123")
      expect(@last_sampling_context[:feature_flag]).to be true
    end
  end

  describe "deterministic sampling behavior" do
    before do
      Sentry.configuration.traces_sampler = ->(_) { 0.5 }
    end

    it "produces consistent results for same trace_id" do
      trace_id = "771a43a4192642f0b136d5159a501700"

      # Create multiple transactions with same trace_id
      results = 5.times.map do
        transaction = Sentry::Transaction.new(hub: hub, trace_id: trace_id)
        hub.start_transaction(transaction: transaction)
        transaction.sampled
      end

      # All should have same sampling decision
      expect(results.uniq.length).to eq(1)
    end

    it "produces different results for different trace_ids" do
      results = 10.times.map do
        transaction = Sentry::Transaction.new(hub: hub)
        hub.start_transaction(transaction: transaction)
        transaction.sampled
      end

      # Should have mix of true/false (very unlikely to be all same)
      expect(results.uniq.length).to be > 1
    end
  end
end
