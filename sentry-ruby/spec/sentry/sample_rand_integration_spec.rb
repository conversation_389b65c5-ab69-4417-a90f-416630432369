# frozen_string_literal: true

RSpec.describe "Sample Rand Integration" do
  before do
    perform_basic_setup do |config|
      config.traces_sample_rate = 0.5
      config.traces_sampler = lambda do |sampling_context|
        # Example of proper parent sampling inheritance
        parent_sample_rate = sampling_context[:parent_sample_rate]
        if parent_sample_rate
          parent_sample_rate
        else
          0.5
        end
      end
    end
  end

  describe "end-to-end propagated sampling" do
    it "maintains consistent sampling across distributed trace" do
      # Step 1: Start a new trace (head SDK)
      root_transaction = Sentry.start_transaction(name: "root", op: "http.server")

      # Step 2: Simulate outgoing HTTP request headers
      Sentry.get_current_scope.set_span(root_transaction)
      headers = Sentry.get_trace_propagation_headers

      expect(headers).to include("sentry-trace", "baggage")
      expect(headers["baggage"]).to include("sentry-sample_rand=")

      # Extract sample_rand from baggage header for verification
      baggage = root_transaction.get_baggage
      sample_rand_from_baggage = baggage.items["sample_rand"]
      expect(sample_rand_from_baggage).to match(/\A\d+\.\d{6}\z/) # Format: 0.123456

      # Step 3: Simulate incoming request in downstream service
      sentry_trace = headers["sentry-trace"]
      baggage_header = headers["baggage"]

      # Create child transaction from incoming headers
      child_transaction = Sentry::Transaction.from_sentry_trace(
        sentry_trace,
        baggage: baggage_header
      )

      expect(child_transaction).not_to be_nil

      # Step 4: Start child transaction and verify consistent sampling
      Sentry.get_current_scope.set_span(child_transaction)
      started_child = Sentry.start_transaction(transaction: child_transaction)

      # Both transactions should have same sampling decision
      expect(started_child.sampled).to eq(root_transaction.sampled)
      expect(started_child.effective_sample_rate).to eq(root_transaction.effective_sample_rate)

      # Step 5: Verify baggage propagation continues
      # When child becomes head SDK, it should populate baggage with sample_rand
      child_headers = Sentry.get_trace_propagation_headers
      expect(child_headers["baggage"]).to include("sentry-sample_rand=")

      # The sample_rand should be consistent across the trace
      child_baggage = started_child.get_baggage
      child_sample_rand = child_baggage.items["sample_rand"]
      expect(child_sample_rand).to eq(sample_rand_from_baggage)
    end

    it "handles missing sample_rand gracefully" do
      # Simulate incoming trace from older SDK without sample_rand
      sentry_trace = "771a43a4192642f0b136d5159a501700-7c51afd529da4a2a-1"
      baggage_header = "sentry-trace_id=771a43a4192642f0b136d5159a501700,sentry-sample_rate=0.25"

      # Create transaction from incoming trace
      transaction = Sentry::Transaction.from_sentry_trace(sentry_trace, baggage: baggage_header)

      expect(transaction).not_to be_nil

      # Should generate deterministic sample_rand based on trace_id and sampling decision
      # For sampled=true and sample_rate=0.25, sample_rand should be < 0.25
      expected_sample_rand = Sentry::Utils::SampleRand.generate_from_sampling_decision(
        true, # parent_sampled from sentry-trace (ends with -1)
        0.25, # sample_rate from baggage
        "771a43a4192642f0b136d5159a501700" # trace_id
      )

      # Verify the transaction has the expected sample_rand
      # Note: For incoming transactions, sample_rand is not added to baggage
      # but is used internally for sampling decisions
      expect(expected_sample_rand).to be >= 0.0
      expect(expected_sample_rand).to be < 1.0
      expect(expected_sample_rand).to be < 0.25

      # Should be deterministic for same trace
      expected_sample_rand2 = Sentry::Utils::SampleRand.generate_from_sampling_decision(
        true, 0.25, "771a43a4192642f0b136d5159a501700"
      )
      expect(expected_sample_rand2).to eq(expected_sample_rand)

      # Verify baggage preserves incoming data without adding sample_rand
      baggage = transaction.get_baggage
      expect(baggage.items).to eq({
        "trace_id" => "771a43a4192642f0b136d5159a501700",
        "sample_rate" => "0.25"
      })
    end

    it "works with PropagationContext for tracing without performance" do
      # Simulate incoming request headers
      env = {
        "HTTP_SENTRY_TRACE" => "771a43a4192642f0b136d5159a501700-7c51afd529da4a2a-1",
        "HTTP_BAGGAGE" => "sentry-trace_id=771a43a4192642f0b136d5159a501700,sentry-sample_rand=0.123456"
      }

      # Create PropagationContext from incoming headers
      scope = Sentry.get_current_scope
      propagation_context = Sentry::PropagationContext.new(scope, env)

      # Should use sample_rand from incoming baggage
      expect(propagation_context.sample_rand).to eq(0.123456)

      # Should include sample_rand in outgoing baggage
      baggage = propagation_context.get_baggage
      expect(baggage.items["sample_rand"]).to eq("0.123456")
    end

    it "demonstrates deterministic sampling behavior" do
      # Test that same trace_id always produces same sampling decision
      trace_id = "771a43a4192642f0b136d5159a501700"

      # Create multiple transactions with same trace_id
      results = 5.times.map do
        transaction = Sentry::Transaction.new(trace_id: trace_id, hub: Sentry.get_current_hub)
        Sentry.start_transaction(transaction: transaction)
        transaction.sampled
      end

      # All should have same sampling decision
      expect(results.uniq.length).to eq(1)

      # Verify the sample_rand is deterministic too by checking baggage
      sample_rands = 5.times.map do
        transaction = Sentry::Transaction.new(trace_id: trace_id, hub: Sentry.get_current_hub)
        baggage = transaction.get_baggage
        baggage.items["sample_rand"]
      end

      expect(sample_rands.uniq.length).to eq(1)

      # Verify the expected deterministic value
      expected_sample_rand = Sentry::Utils::SampleRand.format(
        Sentry::Utils::SampleRand.generate_from_trace_id(trace_id)
      )
      expect(sample_rands.first).to eq(expected_sample_rand)
    end

    it "works with custom traces_sampler" do
      # Set up custom traces_sampler that uses parent_sample_rate
      sampling_contexts = []
      Sentry.configuration.traces_sampler = lambda do |context|
        sampling_contexts << context
        context[:parent_sample_rate] || 0.5
      end

      # Create parent transaction with baggage
      baggage = Sentry::Baggage.new({ "sample_rate" => "0.75" })
      parent_transaction = Sentry::Transaction.new(
        hub: Sentry.get_current_hub,
        baggage: baggage,
        sample_rand: 0.6
      )

      # Start transaction through hub
      Sentry.start_transaction(transaction: parent_transaction)

      # Verify traces_sampler received parent_sample_rate
      expect(sampling_contexts.last[:parent_sample_rate]).to eq(0.75)

      # Verify sampling decision uses sample_rand
      # sample_rand (0.6) < sample_rate (0.75), so should be sampled
      expect(parent_transaction.sampled).to be true

      # Verify sample_rand is preserved in baggage
      transaction_baggage = parent_transaction.get_baggage
      expect(transaction_baggage.items["sample_rand"]).to eq("0.600000")
    end

    it "handles edge case with invalid sample_rand in baggage" do
      # Simulate incoming trace with invalid sample_rand
      sentry_trace = "771a43a4192642f0b136d5159a501700-7c51afd529da4a2a-1"
      baggage_header = "sentry-trace_id=771a43a4192642f0b136d5159a501700,sentry-sample_rand=1.5"

      # Should fall back to generating sample_rand from trace_id
      transaction = Sentry::Transaction.from_sentry_trace(sentry_trace, baggage: baggage_header)

      expect(transaction).not_to be_nil

      # Should fall back to deterministic generation based on trace_id
      expected_sample_rand = Sentry::Utils::SampleRand.generate_from_trace_id("771a43a4192642f0b136d5159a501700")

      expect(expected_sample_rand).to be >= 0.0
      expect(expected_sample_rand).to be < 1.0

      # Verify baggage preserves incoming data (including invalid sample_rand)
      baggage = transaction.get_baggage
      expect(baggage.items).to eq({
        "trace_id" => "771a43a4192642f0b136d5159a501700",
        "sample_rand" => "1.5" # Invalid value is preserved in incoming baggage
      })
    end
  end
end
